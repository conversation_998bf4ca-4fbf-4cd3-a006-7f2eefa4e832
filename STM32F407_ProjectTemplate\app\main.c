/*
 * ������������Ӳ�������������չ����Ӳ�����Ϲ���ȫ����Դ
 * �����������www.lckfb.com
 * ����֧�ֳ�פ��̳���κμ������⻶ӭ��ʱ����ѧϰ
 * ������̳��https://oshwhub.com/forum
 * ��עbilibili�˺ţ������������塿���������ǵ����¶�̬��
 * ��������׬Ǯ���������й�����ʦΪ����
 * 

 Change Logs:
 * Date           Author       Notes
 * 2024-03-14     LCKFB-LP    first version
 */
#include "board.h"
#include "bsp_uart.h"
#include <stdio.h>
#include "oled.h"

int main(void)
{
	
	board_init();
	
	uart1_init(115200U);

	OLED_Init();     //��ʼ��OLED
	OLED_Clear();
	
	while(1) 
	{
		OLED_ShowString(0,0,(uint8_t *)"ABC",8,1);//6*8 ��ABC��
		OLED_ShowString(0,8,(uint8_t *)"ABC",12,1);//6*12 ��ABC��
		OLED_ShowString(0,20,(uint8_t *)"ABC",16,1);//8*16 ��ABC��
		OLED_ShowString(0,36,(uint8_t *)"ABC",24,1);//12*24 ��ABC��
		OLED_Refresh();
		delay_ms(100);
					
	}
	

}
